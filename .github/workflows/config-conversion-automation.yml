name: Automatic synchronization of application.yml to ors-config.yml and ors-config.env
on:
  pull_request:
    branches:
      - main
      - releases/**
    types: [ opened, ready_for_review, reopened, synchronize ]
    paths:
      - 'ors-api/src/main/resources/application.yml'

jobs:
  sync_config:
    name: Synchronize changes in application.yml to ors-config.yml and ors-config.env
    runs-on: ubuntu-latest
    permissions:
      contents: write
    if: (! github.event.pull_request.draft) && github.event.pull_request.state == 'open'
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.head_ref }}
      - name: yq - portable yaml processor
        uses: mikefarah/yq@v4.40.5
      - name: validate application.yml
        run: .github/utils/yml_config_validation.sh ors-api/src/main/resources/application.yml
      - name: Convert application.yml to ors-config.yml and ors-config.env
        run: |
          # Print yq version
          yq --version
          .github/utils/yml_config_to_ors_config_conversion.sh ors-api/src/main/resources/application.yml ors-config.yml
          .github/utils/yml_config_to_properties_conversion.sh ors-api/src/main/resources/application.yml ors-config.env
      - uses: MichaelsJP/git-auto-commit-action@v5
        with:
          commit_message: 'chore(config): automatic conversion of application.yml to ors-config.yml and ors-config.env'
