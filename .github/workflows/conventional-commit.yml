# This workflow will build a Java project with <PERSON><PERSON>
# For more information see: https://help.github.com/actions/language-and-framework-guides/building-and-testing-java-with-maven

name: Conventional commit checks

on:
  pull_request:
    types: [ edited, opened, ready_for_review, review_requested, reopened, closed, synchronize ]

jobs:
  convention_commit_check:
    name: Run conventional commit style check
    runs-on: ubuntu-latest
    steps:
      - name: Check the commit style against the 'conventional commit styling'
        uses: MichaelsJP/action-conventional-commits@master
        with:
          VALID_LABELS: '["feat","fix","docs","style","refactor","test","build","perf","ci","chore","revert","merge","wip"]'
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  conventional_commit_pr_title_check:
    name: Check the PR title against the 'conventional commit styling'
    runs-on: ubuntu-latest
    permissions:
      statuses: write
    steps:
      - uses: aslafy-z/conventional-pr-title-action@v3
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
