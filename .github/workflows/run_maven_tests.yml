# This workflow will build a Java project with <PERSON><PERSON>
# For more information see: https://help.github.com/actions/language-and-framework-guides/building-and-testing-java-with-maven

name: Java CI with Maven

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "**" ]

jobs:
  run_tests:
    name: Run unit and integration tests
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: '17'
      - name: Cache SonarCloud packages
        uses: actions/cache@v4
        with:
          path: ~/.sonar/cache
          key: ${{ runner.os }}-sonar-${{ hashFiles('**/pom.xml') }}
          restore-keys: ${{ runner.os }}-sonar
      - name: Cache Maven packages
        uses: actions/cache@v4
        with:
          path: ~/.m2
          key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
          restore-keys: ${{ runner.os }}-m2
      - name: Download maven dependencies
        run: ./mvnw package -Dmaven.test.skip=true -B dependency:go-offline dependency:resolve-plugins dependency:resolve -q
      - name: run unit tests
        run: ./mvnw -B verify -Papitests -T $(nproc) -DCI=true
      - name: Run the unit tests a second time with jacoco
        run: |
          # Ensure test stability across graph reloads.
          ./mvnw -B verify -Papitests -T $(nproc) jacoco:report
      - name: Scan results with Sonar
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        run: ./mvnw -B org.sonarsource.scanner.maven:sonar-maven-plugin:4.0.0.4121:sonar
      - name: Rocket.Chat Notification
        uses: RocketChat/Rocket.Chat.GitHub.Action.Notification@1.1.1
        env:
          ROCKETCHAT_WEBHOOK: ${{ secrets.ROCKETCHAT_WEBHOOK }}
        if: env.ROCKETCHAT_WEBHOOK != null
        with:
          type: ${{ job.status }}
          job_name: '*ORS CI Test*'
          token: ${{ secrets.PROJECT_AUTOMATION }}
