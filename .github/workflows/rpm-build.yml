name: Package Java WAR into RPM

on:
#  release:
 #   types:
  #    - created
  workflow_dispatch:

env:
  HEALTH_WAIT_TIME: 200

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: '17'

      - name: Cache Maven packages
        uses: actions/cache@v4
        with:
          path: ~/.m2
          key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
          restore-keys: ${{ runner.os }}-m2

      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Build Java WAR
        run: ./mvnw clean package -DskipTests

      - name: Get mvn project.version
        run: |
          projectVersion=$(./mvnw help:evaluate -Dexpression=project.version -q -DforceStdout | sed 's/-SNAPSHOT/.SNAPSHOT/g')
          echo "ORS_VERSION=$projectVersion" >> "$GITHUB_ENV"

      - name: RPMBuild
        run: |
          export ORS_VERSION=${{ env.ORS_VERSION }}
          sudo apt-get install rpm
          mkdir -p ~/rpmbuild/{BUILD,RPMS,SPECS,SRPMS}
          cp ors-api/target/ors.war ~/rpmbuild/BUILD/
          rpmbuild -bb .rpm-packaging/ors-war.spec
          mv ~/rpmbuild/RPMS/noarch/openrouteservice-${{ env.ORS_VERSION }}-1.noarch.rpm .rpm-packaging/

      - name: Test with Tomcat
        run: |
          cp ors-api/src/test/files/elevation/srtm_38_03.gh .rpm-packaging/
          cp ors-api/src/test/files/heidelberg.test.pbf .rpm-packaging/
          cd .rpm-packaging
          docker build . -t ors-rpm-tomcat --build-arg ors_version=${{ env.ORS_VERSION }}
          docker run -it -d -p 8080:8080 --name ors-tomcat ors-rpm-tomcat
          ../.github/utils/url_check.sh 127.0.0.1 8080 /ors/v2/health 200 ${{ env.HEALTH_WAIT_TIME }}


#      - name: Attach RPM package to release
#        uses: actions/upload-release-asset@v1
#        with:
#          upload_url: ${{ github.event.release.upload_url }}
#          asset_path: ~/rpmbuild/RPMS/noarch/my-app-1.0.0-1.noarch.rpm
#          asset_name: my-app-1.0.0-1.noarch.rpm
#          asset_content_type: application/x-rpm