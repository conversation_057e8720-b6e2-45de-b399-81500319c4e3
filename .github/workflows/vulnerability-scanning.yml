name: "Vulnerability scan"

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]
  schedule:
    - cron: '0 0 * * MON'

permissions:
  contents: read

env:
  TEST_IMAGE_NAME: 'local/openrouteservice:test'

jobs:
  prepare_environment:
    name: Prepare the environment variables
    runs-on: ubuntu-latest
    outputs:
      test_image_name: ${{ env.TEST_IMAGE_NAME }}
    steps:
      - run: |
          echo "Publish environment variables"
  Anchore-Jar-War-Build-Scan:
    name: Grype scan jar and war file
    runs-on: ubuntu-latest
    permissions:
      actions: read
      contents: read
      security-events: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: '17'
      - name: Cache Maven packages
        uses: actions/cache@v4
        with:
          path: ~/.m2
          key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
          restore-keys: ${{ runner.os }}-m2
      - name: Download maven dependencies
        run: ./mvnw package -Dmaven.test.skip=true -B dependency:go-offline dependency:resolve-plugins dependency:resolve -q
      - name: Build jar and war file
        run: |
          ./mvnw -B package -DskipTests -DCI=true
          # Copy the .jar file to a custom location where grype can find it
          mkdir -p ors-api/target/grype
          cp ors-api/target/ors.jar ors-api/target/grype/ors.jar
          ./mvnw -B package -DskipTests -PbuildWar -DCI=true
          cp ors-api/target/ors.war ors-api/target/grype/ors.war
      - name: Run the Anchore Grype scan action to console
        uses: anchore/scan-action@v5
        with:
          path: "ors-api/target/grype/"
          fail-build: false
          output-format: table
      - name: Run the Anchore Grype scan action to SARIF
        uses: anchore/scan-action@v5
        id: scan
        with:
          path: "ors-api/target/grype/"
          fail-build: false
          output-format: sarif
      - name: Upload vulnerability report
        uses: github/codeql-action/upload-sarif@v3
        with:
          sarif_file: ${{ steps.scan.outputs.sarif }}
          category: Grype-War-Scan
  Anchore-Docker-Image-Scan:
    name: Grype scan ${{ matrix.platform }} image
    runs-on: ${{ matrix.image }}
    needs:
      - prepare_environment
    permissions:
      actions: read
      contents: read
      security-events: write
    strategy:
      matrix:
        platform: [ linux/amd64,linux/arm64/v8 ]
        image: [ ubuntu-latest ]
        # linux/arm64/v8 is emulated with qemu and takes ages to build the graph.
        # Only run linux/arm64/v8 tests on ready PR and main.
        isDraftPR:
          - ${{ github.event_name == 'pull_request' && github.event.pull_request.draft == true }}
        exclude:
          - isDraftPR: true
            platform: linux/arm64/v8
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Set up QEMU for ${{ matrix.platform }}
        uses: docker/setup-qemu-action@v3
        with:
          platforms: ${{ matrix.platform }}
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
        id: buildx
        with:
          install: true
      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: '17'
      - name: Cache Maven packages
        uses: actions/cache@v4
        with:
          path: ~/.m2
          key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
          restore-keys: ${{ runner.os }}-m2
      - name: Prepare the maven cache dependencies
        run: |
          echo "Sync the maven dependencies"
          ./mvnw package -Dmaven.test.skip=true -B dependency:go-offline dependency:resolve-plugins dependency:resolve -q
          # Replace all RUN ./mvnw with RUN --mount=type=cache,target=/root/.m2 ./mvnw
          sed -i 's/RUN \.\/mvnw /RUN --mount=type=cache,target=\/root\/.m2 \.\/mvnw /g' Dockerfile
      - name: inject maven-build-cache into docker
        uses: reproducible-containers/buildkit-cache-dance@v3.1.2
        with:
          cache-map: |
            {
              "/home/<USER>/.m2": "/root/.m2"
            }
      - name: Build image for ${{ matrix.platform }}
        uses: docker/build-push-action@v4
        with:
          context: .
          push: false
          load: true
          tags: ${{ needs.prepare_environment.outputs.test_image_name }}
          platforms: ${{ matrix.platform }}
          cache-from: type=gha
      - name: Run the Anchore Grype scan action to console
        uses: anchore/scan-action@v5
        with:
          image: ${{ needs.prepare_environment.outputs.test_image_name }}
          fail-build: false
          output-format: table
      - name: Run the Anchore Grype scan action to SARIF
        uses: anchore/scan-action@v5
        id: scan
        with:
          image: ${{ needs.prepare_environment.outputs.test_image_name }}
          fail-build: false
          output-format: sarif
      - name: Upload vulnerability report
        uses: github/codeql-action/upload-sarif@v3
        with:
          sarif_file: ${{ steps.scan.outputs.sarif }}
          category: Grype-Docker-Image-${{ matrix.platform }}
