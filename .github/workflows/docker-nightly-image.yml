name: Docker Nightly Image CI
run-name: Build ${{ inputs.branch }} - ${{ inputs.user }}

on:
  schedule:
    - cron: '15 1 * * *'
  workflow_dispatch:
    inputs:
      repository:
        description: 'GitHub repository to create image off.'
        required: true
        default: 'GIScience/openrouteservice'
      branch:
        description: 'GitHub branch to create image off.'
        required: true
        default: 'main'
      tag:
        description: 'Name of the docker tag to create.'
        required: true
        default: 'nightly'
      ignore-24h-commit-check:
        description: 'Build image regardless of last commit date.'
        required: true
        type: boolean
        default: false
      user:
        description: ''
        required: false
        default: 'schedule'

jobs:
  check-commits:
    runs-on: ubuntu-latest
    outputs:
      new_commits: ${{ steps.check.outputs.NEW_COMMITS }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          repository: ${{ inputs.repository || 'GIScience/openrouteservice' }}
          ref: ${{ inputs.branch || 'main' }}

      - name: Check for new commits since 24 h ago
        id: check
        run: |
          export LOG_LINES=$(git log --oneline --since '24 hours ago')
          export NUM_LINES=$(echo "$LOG_LINES" | grep -c .)
          if [ "$NUM_LINES" -eq "0" ]; then
            export NEW_COMMITS="false"
          else
            export NEW_COMMITS="true"
          fi
          echo "NEW_COMMITS=$NEW_COMMITS" >> "$GITHUB_OUTPUT"

  build:
    needs: check-commits
    runs-on: ubuntu-latest
    if: inputs.ignore-24h-commit-check || needs.check-commits.outputs.new_commits == 'true'
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          repository: ${{ inputs.repository || 'GIScience/openrouteservice' }}
          ref: ${{ inputs.branch || 'main' }}

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3.0.0

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3.0.0

      - name: Login to DockerHub
        uses: docker/login-action@v3.0.0
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Build and push
        uses: docker/build-push-action@v5.1.0
        with:
          context: .
          platforms: linux/amd64,linux/arm64/v8
          provenance: false
          push: true
          cache-from: type=gha
          cache-to: type=gha,mode=max
          tags: ${{ secrets.DOCKER_USERNAME }}/openrouteservice:${{ inputs.tag || 'nightly' }}
