# This workflow will build a Java project with <PERSON><PERSON>
# For more information see: https://help.github.com/actions/language-and-framework-guides/building-and-testing-java-with-maven

name: Integration test suite

on:
  push:
    branches: [ "main" ]
    paths-ignore:
      - 'docs/**'
      - 'ors-api/src/test/java/**'
      - 'ors-api/src/test/resources/**'
      - 'ors-engine/src/test/**'
      - 'ors-report-aggregation/**'
      - '.rpm-packaging/**'
  pull_request:
    branches: [ "main" ]
    types:
      - opened
      - reopened
      - synchronize
      - ready_for_review
    paths-ignore:
      - 'docs/**'
      - 'ors-api/src/test/java/**'
      - 'ors-api/src/test/resources/**'
      - 'ors-engine/src/test/**'
      - 'ors-report-aggregation/**'
      - '.rpm-packaging/**'

jobs:
  build-default-builder:
    name: Build default builder image
    runs-on: ubuntu-latest
    outputs:
      builder_image_id: ${{ steps.docker_build.outputs.imageid }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Set up JDK 17
        id: setup-java
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: '17'
      - name: Cache Maven packages
        uses: actions/cache@v4
        with:
          path: ~/.m2
          key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
          restore-keys: ${{ runner.os }}-m2
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          version: latest
      - name: Prepare the maven cache dependencies
        run: |
          echo "Sync the maven dependencies"
          ./mvnw package -Dmaven.test.skip=true -B dependency:go-offline dependency:resolve-plugins dependency:resolve -q
          # Replace all RUN ./mvnw with RUN --mount=type=cache,target=/root/.m2 ./mvnw
          sed -i 's/RUN \.\/mvnw /RUN --mount=type=cache,target=\/root\/.m2 \.\/mvnw /g' ors-test-scenarios/src/test/resources/Builder.Dockerfile
      - name: inject maven-build-cache into docker
        uses: reproducible-containers/buildkit-cache-dance@v3.1.2
        with:
          cache-map: |
            {
              "/home/<USER>/.m2": "/root/.m2"
            }
      - name: Build ors-test-scenarios-builder
        id: docker_build
        uses: docker/build-push-action@v6
        with:
          context: .
          push: false
          load: false
          file: ./ors-test-scenarios/src/test/resources/Builder.Dockerfile
          tags: ors-test-scenarios-builder:latest
          target: ors-test-scenarios-builder
          cache-from: type=gha
          cache-to: type=gha,mode=max
      - name: Cache the graph
        id: cache_graph
        uses: actions/cache@v4
        with:
          path: |
            ors-test-scenarios/graphs-integrationtests
          key: ${{ runner.os }}-ors-test-scenarios-builder-images-${{ steps.docker_build.outputs.imageid }}
      - name: Load the ors-test-scenarios-builder image
        if: steps.cache_graph.outputs.cache-hit != 'true'
        uses: docker/build-push-action@v6
        with:
          context: .
          push: false
          load: false
          file: ./ors-test-scenarios/src/test/resources/Builder.Dockerfile
          tags: ors-test-scenarios-builder:latest
          target: ors-test-scenarios-builder
          cache-from: type=gha
          cache-to: type=gha,mode=max
      - name: Load ors-test-scenarios-jar-builder image
        if: steps.cache_graph.outputs.cache-hit != 'true'
        uses: docker/build-push-action@v6
        with:
          context: .
          load: true
          file: ./ors-test-scenarios/src/test/resources/Builder.Dockerfile
          tags: ors-test-scenarios-jar-builder:latest
          target: ors-test-scenarios-jar-builder
          cache-from: type=gha
          cache-to: type=gha,mode=min
      - name: Build shared graph
        if: steps.cache_graph.outputs.cache-hit != 'true'
        run: |
          # Build the shared graph
          ./mvnw -pl ors-test-scenarios -q -Dtest=integrationtests.OneShotGraphBuilderTest\#oneShotGraphBuilder test \
            -Dcontainer.builder.use_prebuild=true \
            -Dcontainer.run.scenario=jar
        env:
          ONE_SHOT_GRAPH_BUILDER: true
  integration-tests:
    name: Integration tests
    runs-on: ubuntu-22.04
    needs:
      - build-default-builder
    strategy:
      matrix:
        test-scenario: [ "maven", "jar", "war" ]
        test-class:
          - integrationtests.ConfigEnvironmentTest
          - integrationtests.ConfigFileTest
          - integrationtests.ConfigLookupTest
          - integrationtests.GeoToolsTest
          - integrationtests.GraphRepoTest
      fail-fast: false
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: '17'
      - name: Cache local Maven repository
        uses: actions/cache/restore@v4
        with:
          path: ~/.m2/repository
          key: ${{ runner.os }}-maven-${{ hashFiles('**/pom.xml') }}
          restore-keys: |
            ${{ runner.os }}-maven-
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          version: latest
      - name: inject maven-build-cache into docker
        uses: reproducible-containers/buildkit-cache-dance@v3.1.2
        with:
          cache-map: |
            {
              "/home/<USER>/.m2": "/root/.m2"
            }
      - name: Prepare the maven cache dependencies
        run: |
          # Replace all RUN ./mvnw with RUN --mount=type=cache,target=/root/.m2 ./mvnw
          sed -i 's/RUN \.\/mvnw /RUN --mount=type=cache,target=\/root\/.m2 \.\/mvnw /g' ors-test-scenarios/src/test/resources/Builder.Dockerfile
      - name: Restore cached graph
        id: restore-cached-image
        uses: actions/cache/restore@v4
        with:
          path: |
            ors-test-scenarios/graphs-integrationtests
          key: ${{ runner.os }}-ors-test-scenarios-builder-images-${{ needs.build-default-builder.outputs.builder_image_id }}
      - name: Load ors-test-scenarios-builder image
        uses: docker/build-push-action@v6
        with:
          context: .
          load: true
          file: ./ors-test-scenarios/src/test/resources/Builder.Dockerfile
          tags: ors-test-scenarios-builder:latest
          target: ors-test-scenarios-builder
          cache-from: type=gha
      - name: Build the ${{ matrix.test-scenario }} builder image
        id: docker_build
        uses: docker/build-push-action@v6
        with:
          context: .
          load: true
          file: ./ors-test-scenarios/src/test/resources/Builder.Dockerfile
          tags: ors-test-scenarios-${{ matrix.test-scenario }}-builder:latest
          target: ors-test-scenarios-${{ matrix.test-scenario }}-builder
          cache-from: type=gha
      - name: Run integration tests
        run: |
          # List the cached images
          docker image ls -a
          # Run only a subset for now
          ./mvnw -pl ors-test-scenarios -Dtest=${{ matrix.test-class }} test \
            -Djunit.jupiter.execution.parallel.config.fixed.parallelism=4 \
            -Dcontainer.run.scenario=${{ matrix.test-scenario }} \
            -Dcontainer.builder.use_prebuild=true -q
