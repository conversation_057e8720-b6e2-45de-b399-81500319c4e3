# OpenRouteService Setup - New PC Commands

## Step-by-Step Commands for New PC

### 1. Install Prerequisites
```bash
# macOS
brew install openjdk@21

# Ubuntu/Debian
sudo apt update && sudo apt install openjdk-21-jdk wget

# CentOS/RHEL
sudo yum install java-21-openjdk-devel wget

# Verify Java
java -version
```

### 2. <PERSON>lone and Setup Repository
```bash
# Clone repository
git clone https://github.com/GIScience/openrouteservice.git
cd openrouteservice

# Create required directories
mkdir -p ors-docker/graphs ors-docker/logs ors-docker/files

# Download India OSM file (1.5GB - takes 10-15 minutes)
cd ors-docker/files
wget https://download.geofabrik.de/asia/india-latest.osm.pbf
cd ../..
```

### 3. Build the Project
```bash
# Build ORS (takes 5-10 minutes)
./mvnw clean package -DskipTests
```

### 4. Configure for India Map
```bash
# Backup original config
cp ors-config.yml ors-config.yml.backup

# Edit configuration file
nano ors-config.yml
```

**Replace the source_file line with:**
```yaml
ors:
  engine:
    profile_default:
      build:
        source_file: ./ors-docker/files/india-latest.osm.pbf
        graphs_root_path: ./ors-docker/graphs
    profiles:
      driving-car:
        enabled: true
        elevation: false
        elevation_smoothing: false
        elevation_provider: none
        routing-options:
          maximum_distance: 4000000
        preparation:
          min_network_size: 200
          min_one_way_network_size: 200
      walking:
        enabled: true
        elevation: false
        elevation_smoothing: false
        elevation_provider: none
        routing-options:
          maximum_distance: 2000000
      cycling-regular:
        enabled: true
        elevation: false
        elevation_smoothing: false
        elevation_provider: none
        routing-options:
          maximum_distance: 2000000
```

### 5. Run the Service (Background Mode)
```bash
# Start with nohup (survives screen off/logout)
nohup java -Xms8g -Xmx12g -jar ors-api/target/ors.jar > ors-build.log 2>&1 &

# Save process ID for later
echo $! > ors.pid
```

## How to Monitor Build Progress - DETAILED

### 1. Real-time Log Monitoring
```bash
# Watch live progress (most detailed)
tail -f ors-build.log

# Watch with timestamps
tail -f ors-build.log | while read line; do echo "$(date '+%H:%M:%S'): $line"; done
```

### 2. Health Status Check
```bash
# Simple status check
curl -s http://localhost:8082/ors/v2/health

# Formatted output
curl -s http://localhost:8082/ors/v2/health | python3 -m json.tool

# Expected responses:
# Building: {"status": "not ready"}
# Ready: {"status": "ready"}
```

### 3. Progress Phases and Time Estimates

#### **Phase 1: Graph Import (30-45 minutes)**
Look for these log messages:
```
start creating graph from ors-docker/files/india-latest.osm.pbf
nodes: X,XXX,XXX, edges: XX,XXX,XXX
```

#### **Phase 2: CH Preparation (15-30 minutes per profile)**
Look for:
```
Creating CH preparations
calling CH prepare.doWork for profile 'car_ors_fastest_with_turn_costs'
edge, nodes: X,XXX,XXX, shortcuts: XXX,XXX
```

**Progress indicators:**
- Node count decreasing: `nodes: 17,708,874` → `nodes: 10,000,000` → `nodes: 0`
- Shortcuts increasing: `shortcuts: 0` → `shortcuts: 20,000,000+`

#### **Phase 3: LM Preparation (10-15 minutes per profile)**
Look for:
```
Creating LM preparations
calling LM prepare.doWork for limited_access|fastest|car_ors
```

#### **Phase 4: Additional Profiles (20-30 minutes each)**
- Walking profile
- Cycling profile

### 4. File System Progress Monitoring
```bash
# Watch graph files being created
watch -n 30 'ls -lah ors-docker/graphs/'

# Monitor graph directory size growth
watch -n 60 'du -sh ors-docker/graphs/ && echo "Files:" && find ors-docker/graphs/ -name "*" | wc -l'

# Check specific profile progress
ls -la ors-docker/graphs/driving-car/
```

### 5. Memory and CPU Monitoring
```bash
# Check Java process status
ps aux | grep ors.jar

# Monitor memory usage
watch -n 30 'ps aux | grep ors.jar | grep -v grep'

# Detailed process info
top -p $(pgrep -f ors.jar)
```

### 6. Progress Percentage Calculation

#### **For CH Preparation Phase:**
```bash
# Extract current node count from logs
tail -20 ors-build.log | grep "edge, nodes:" | tail -1

# Calculate progress:
# Start: ~17,700,000 nodes
# Current: X nodes (from log)
# Progress = (17,700,000 - X) / 17,700,000 * 100
```

#### **Example Progress Script:**
```bash
#!/bin/bash
# Save as check_progress.sh

echo "=== ORS Build Progress ==="
echo "Time: $(date)"

# Check if service is running
if pgrep -f ors.jar > /dev/null; then
    echo "✅ Service is running (PID: $(pgrep -f ors.jar))"
else
    echo "❌ Service not running"
    exit 1
fi

# Check health status
STATUS=$(curl -s http://localhost:8082/ors/v2/health 2>/dev/null || echo "No response")
echo "Health Status: $STATUS"

# Get latest progress from logs
echo -e "\n📊 Latest Progress:"
tail -5 ors-build.log | grep -E "(nodes:|shortcuts:|Creating|Finished)"

# Check graph files
echo -e "\n📁 Graph Files:"
if [ -d "ors-docker/graphs/driving-car" ]; then
    echo "Driving-car: $(ls ors-docker/graphs/driving-car/ | wc -l) files"
fi
if [ -d "ors-docker/graphs/walking" ]; then
    echo "Walking: $(ls ors-docker/graphs/walking/ | wc -l) files"
fi
if [ -d "ors-docker/graphs/cycling-regular" ]; then
    echo "Cycling: $(ls ors-docker/graphs/cycling-regular/ | wc -l) files"
fi

# Memory usage
echo -e "\n💾 Memory Usage:"
ps aux | grep ors.jar | grep -v grep | awk '{print "CPU: "$3"%, Memory: "$4"%, RSS: "$6/1024"MB"}'
```

### 7. Automated Progress Monitoring
```bash
# Make progress script executable
chmod +x check_progress.sh

# Run every 5 minutes
watch -n 300 './check_progress.sh'

# Or run once
./check_progress.sh
```

### 8. Completion Indicators

#### **Service Ready:**
```bash
curl http://localhost:8082/ors/v2/health
# Returns: {"status": "ready"}
```

#### **Log Messages:**
```
Finished building queue
took: XXXs, graph now - num edges: XX,XXX,XXX
RoutingProfileManager: Total initialization time: XXX.XXs
```

#### **Test Routing:**
```bash
# Test Mumbai to Delhi route
curl -X POST "http://localhost:8082/ors/v2/directions/driving-car" \
  -H "Content-Type: application/json" \
  -d '{
    "coordinates": [[72.8777, 19.0760], [77.1025, 28.7041]]
  }'
```

## Time Estimates for India Map

| Phase | Duration | What's Happening |
|-------|----------|------------------|
| Graph Import | 30-45 min | Reading OSM file, creating nodes/edges |
| CH Driving-Car | 15-30 min | Building shortcuts for fast routing |
| LM Driving-Car | 10-15 min | Creating landmarks for distance calc |
| Walking Profile | 20-30 min | Building walking routes |
| Cycling Profile | 20-30 min | Building cycling routes |
| **Total** | **2-4 hours** | Complete setup |

## Quick Commands Summary

```bash
# Check if running
ps aux | grep ors.jar

# Monitor progress
tail -f ors-build.log

# Check status
curl -s http://localhost:8082/ors/v2/health

# Stop service
kill $(cat ors.pid)

# Restart service
nohup java -Xms8g -Xmx12g -jar ors-api/target/ors.jar > ors-build.log 2>&1 &
echo $! > ors.pid
```
