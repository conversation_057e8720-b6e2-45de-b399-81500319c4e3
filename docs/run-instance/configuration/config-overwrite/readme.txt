Since mermaid and plantuml are cumbersome, some diagrams for config overriding were made with draw.io.
This folder is the working directory for the draw.io based diagrams. It contains:

* The draw.io file for future editing (go to https://draw.io and open this file as existing diagram):
    config-overwrite.drawio.xml
* YAML files that were formatted in IntelliJ and copied to draw.io in the browser (with syntax highlighting):
    config-overwrite.in.format.*.yml
* PNG files exported from draw.io:
    config-overwrite-*.png

The exported PNG files are linked from the documentation markdown file run-engine/configuration/index.md.
