
# `ors.engine.elevation`

Settings related to elevation information for graph building.

| key          | type    | description                                                                                                                                                                                                                                                                                                                       | default value       |
|--------------|---------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------|
| preprocessed | boolean | Toggles reading of OSM `ele` node tags on and off. If enabled, graphhoppers' elevation lookup is prevented and all nodes without `ele` tag will default to 0. Intended to be used in combination with the ORS preprocessor                                                                                                        | `false`             |
| cache_clear  | boolean | Keep elevation data once it has been downloaded                                                                                                                                                                                                                                                                                   | `false`             |
| provider     | string  | The name of an elevation provider. Possible values are `multi`, `cgiar` or `srtm`                                                                                                                                                                                                                                                 | `multi`             |
| cache_path   | string  | The path to a directory in which SRTM tiles will be stored                                                                                                                                                                                                                                                                        | `./elevation_cache` |
| data_access  | string  | Defines how a DataAccess object is created. <br> - `MMAP`: memory mapped storage <br> - `RAM_STORE`: in-memory storage with a safe/flush option. Can be used for small areas <br> Further info in the [source code](https://github.com/GIScience/graphhopper/blob/ors_4.0/core/src/main/java/com/graphhopper/storage/DAType.java) | `MMAP`              |
