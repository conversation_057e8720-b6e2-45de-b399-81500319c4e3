# Contributing

We are glad that you decided to contribute to openrouteservice.

There are plenty of ways to help make it an even better routing service.
Whether you are a Java developer who is interested in fixing
some bugs or adding some new functionality, someone who knows an area well and
can update information in OSM so that openrouteservice can take it into
account, or even someone who speaks different languages and is will to provide
some translations for routing instructions, there will be something that you
can help with.

Feel free to create an [issue](https://github.com/GIScience/openrouteservice/issues) and label it accordingly.
If your issue regards the openrouteservice maps client on [maps.openrouteservice.org](https://maps.openrouteservice.org)
please use the [corresponding repository](https://github.com/GIScience/ors-map-client/issues).

## Contributing code

To help you get started, here is a document describing how
to [build openrouteservice from code](/run-instance/building-from-source.md).
We also put together a few [guidelines](https://github.com/GIScience/openrouteservice/blob/main/CONTRIBUTE.md)
to help you in the process and keep the repository clean and tidy.

## Other ways to contribute

* [Contributing translations](contributing-translations)
* [Contributing to this documentation](backend-documentation)
* [Updating OpenStreetMap](https://wiki.openstreetmap.org/wiki/Beginners%27_guide)
