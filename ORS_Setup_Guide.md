# OpenRouteService (ORS) Local Setup Guide

## Overview
This guide shows how to set up OpenRouteService locally with India map data from OpenStreetMap (OSM).

## Prerequisites
- Java 21+ installed
- 16GB+ RAM (24GB recommended for India map)
- 50GB+ free disk space
- macOS/Linux/Windows with Docker support

## Initial Problem Analysis Steps

### 1. Explore the Repository Structure
```bash
# Check what's in the repository
ls -la

# Look for configuration files
find . -name "*.yml" -o -name "*.yaml" -o -name "docker-compose*"

# Check for existing OSM data
find . -name "*.pbf" -o -name "*.osm*"
```

### 2. Examine Existing Configuration
```bash
# Check docker-compose setup
cat docker-compose.yml

# Check custom configuration
cat custom-ors-config.yml

# Check default configuration
cat ors-config.yml
```

### 3. Check for Existing Logs and Graphs
```bash
# Check logs for errors
ls -la ors-docker/logs/
tail -100 ors-docker/logs/ors.log

# Check if graphs were built
ls -la ors-docker/graphs/

# Check OSM file size
ls -lh ors-docker/files/india-latest.osm.pbf
```

### 4. Identify Common Issues
- Memory allocation problems
- Incorrect file paths in configuration
- Docker image download issues
- Graph building failures

## Solution Steps

### Method 1: Docker Approach (If Docker Works)

#### Step 1: Clean Previous Attempts
```bash
# Stop any running containers
docker compose down

# Remove old graphs
rm -rf ors-docker/graphs/*

# Check available system memory
system_profiler SPHardwareDataType | grep "Memory:"  # macOS
free -h  # Linux
```

#### Step 2: Fix Docker Configuration
```bash
# Edit docker-compose.yml
# Change memory allocation to reasonable values:
# JAVA_HEAP_MIN: "8g"
# JAVA_HEAP_MAX: "12g"
# Add memory limits:
# deploy:
#   resources:
#     limits:
#       memory: 16g
```

#### Step 3: Use Official Docker Image
```bash
# Modify docker-compose.yml to use official image
# image: heigit/openrouteservice:latest
# instead of building from source

# Start the service
docker compose up -d

# Monitor logs
docker compose logs -f ors-app
```

### Method 2: Direct Java Approach (Recommended)

#### Step 1: Check Java Installation
```bash
java -version
# Should show Java 21+
```

#### Step 2: Build the Project
```bash
# Use Maven wrapper to build
./mvnw clean package -DskipTests

# This creates: ors-api/target/ors.jar
```

#### Step 3: Configure for India Map
```bash
# Edit ors-config.yml to use India map
# Change source_file from test file to:
# source_file: ./ors-docker/files/india-latest.osm.pbf
# Add graphs_root_path: ./ors-docker/graphs
```

#### Step 4: Run the Service
```bash
# Start with appropriate memory allocation
java -Xms8g -Xmx12g -jar ors-api/target/ors.jar

# Service will start on port 8082
```

## Complete Setup Commands for New PC

### Prerequisites Installation
```bash
# Install Java 21 (macOS with Homebrew)
brew install openjdk@21

# Install Java 21 (Ubuntu/Debian)
sudo apt update
sudo apt install openjdk-21-jdk

# Install Java 21 (CentOS/RHEL)
sudo yum install java-21-openjdk-devel

# Verify Java installation
java -version
```

### Repository Setup
```bash
# Clone the repository
git clone https://github.com/GIScience/openrouteservice.git
cd openrouteservice

# Download India OSM data (if not present)
mkdir -p ors-docker/files
cd ors-docker/files

# Download India map (1.5GB - takes time)
wget https://download.geofabrik.de/asia/india-latest.osm.pbf

cd ../..
```

### Build and Configure
```bash
# Build the project
./mvnw clean package -DskipTests

# Create directories
mkdir -p ors-docker/graphs
mkdir -p ors-docker/logs

# Configure for India map
cp ors-config.yml ors-config.yml.backup
```

### Edit Configuration
Create/edit `ors-config.yml`:
```yaml
ors:
  engine:
    profile_default:
      build:
        source_file: ./ors-docker/files/india-latest.osm.pbf
        graphs_root_path: ./ors-docker/graphs
    profiles:
      driving-car:
        enabled: true
        elevation: false
        elevation_smoothing: false
        elevation_provider: none
        routing-options:
          maximum_distance: 4000000
        preparation:
          min_network_size: 200
          min_one_way_network_size: 200
      walking:
        enabled: true
        elevation: false
        elevation_smoothing: false
        elevation_provider: none
        routing-options:
          maximum_distance: 2000000
      cycling-regular:
        enabled: true
        elevation: false
        elevation_smoothing: false
        elevation_provider: none
        routing-options:
          maximum_distance: 2000000
  logging:
    level_file: INFO
    level_root: INFO
    location: "./ors-docker/logs/"
```

### Run the Service
```bash
# Start the service (adjust memory based on your system)
java -Xms8g -Xmx12g -jar ors-api/target/ors.jar

# For systems with less RAM (minimum 8GB total):
java -Xms4g -Xmx6g -jar ors-api/target/ors.jar
```

## Screen Timeout and Background Running

### Will it continue running if screen goes off?
**YES** - The Java process runs independently of your screen/display state.

### To ensure it keeps running:

#### Option 1: Use `nohup` (Recommended)
```bash
# Start with nohup to prevent termination on logout
nohup java -Xms8g -Xmx12g -jar ors-api/target/ors.jar > ors-output.log 2>&1 &

# Check if it's running
ps aux | grep ors.jar

# Monitor progress
tail -f ors-output.log
```

#### Option 2: Use `screen` or `tmux`
```bash
# Install screen (if not available)
brew install screen  # macOS
sudo apt install screen  # Ubuntu

# Start a screen session
screen -S ors-session

# Run the service inside screen
java -Xms8g -Xmx12g -jar ors-api/target/ors.jar

# Detach from screen: Ctrl+A, then D
# Reattach later: screen -r ors-session
```

#### Option 3: Create a System Service (Advanced)
```bash
# Create systemd service file (Linux)
sudo nano /etc/systemd/system/ors.service
```

## Monitoring Progress

### Health Check
```bash
# Check if service is ready
curl http://localhost:8082/ors/v2/health

# Expected responses:
# Building: {"status":"not ready"}
# Ready: {"status":"ready"}
```

### File System Monitoring
```bash
# Watch graph files being created
watch -n 30 'ls -lah ors-docker/graphs/'

# Monitor log file
tail -f ors-docker/logs/ors.log
```

### Memory Usage
```bash
# Check Java process memory usage
ps aux | grep java
top -p $(pgrep -f ors.jar)
```

## Testing When Ready

### Basic Health Check
```bash
curl http://localhost:8082/ors/v2/health
```

### Simple Routing Test
```bash
# Mumbai to Delhi route
curl -X POST "http://localhost:8082/ors/v2/directions/driving-car" \
  -H "Content-Type: application/json" \
  -d '{
    "coordinates": [[72.8777, 19.0760], [77.1025, 28.7041]]
  }'
```

### Service Information
```bash
curl http://localhost:8082/ors/v2/status
```

## Troubleshooting

### Common Issues and Solutions

#### Out of Memory Error
```bash
# Reduce heap size
java -Xms4g -Xmx6g -jar ors-api/target/ors.jar
```

#### Port Already in Use
```bash
# Check what's using port 8082
lsof -i :8082

# Kill the process or use different port
java -Dserver.port=8083 -Xms8g -Xmx12g -jar ors-api/target/ors.jar
```

#### Graph Building Stuck
```bash
# Remove corrupted graphs and restart
rm -rf ors-docker/graphs/*
java -Xms8g -Xmx12g -jar ors-api/target/ors.jar
```

## Expected Timeline

- **Graph Import**: 30-45 minutes
- **CH Preparation**: 15-30 minutes per profile
- **LM Preparation**: 10-15 minutes per profile
- **Total Time**: 2-4 hours for all profiles

## Hardware Recommendations

- **Minimum**: 8GB RAM, 4 CPU cores, 50GB storage
- **Recommended**: 16GB+ RAM, 8+ CPU cores, SSD storage
- **Optimal**: 32GB RAM, 16+ CPU cores, NVMe SSD

## How I Figured Out the Solution

### Diagnostic Process I Followed:

1. **Repository Exploration**: Used `ls`, `find`, and `cat` to understand structure
2. **Log Analysis**: Checked `ors-docker/logs/ors.log` for error patterns
3. **Configuration Review**: Examined all `.yml` files for misconfigurations
4. **Memory Analysis**: Used `system_profiler` to check available RAM
5. **Process Monitoring**: Used `ps aux` and `docker ps` to check running services
6. **File System Check**: Verified OSM file existence and size
7. **Iterative Testing**: Tried Docker first, then switched to direct Java approach

### Key Issues Identified:
- Memory allocation was too high (14GB) causing instability
- Docker image download was extremely slow
- Configuration pointed to test file instead of India map
- Graphs directory needed proper setup

### Solution Strategy:
1. **Bypass Docker**: Use direct Java execution for faster setup
2. **Optimize Memory**: Reduce heap size to 8-12GB for stability
3. **Fix Configuration**: Point to correct OSM file and graph directory
4. **Monitor Progress**: Set up multiple monitoring methods

## Screen Timeout - Detailed Answer

### ✅ **YES, it will continue running when screen goes off**

The Java process is independent of your display/screen state. However, there are important considerations:

### Potential Issues:
- **SSH disconnection**: If running over SSH, connection loss might terminate process
- **Laptop sleep**: If laptop goes to sleep, process will pause
- **Terminal closure**: Closing terminal window might kill the process

### Bulletproof Solutions:

#### 1. **nohup Method** (Simplest)
```bash
# Start with nohup - survives logout/disconnect
nohup java -Xms8g -Xmx12g -jar ors-api/target/ors.jar > ors.log 2>&1 &

# Get process ID
echo $! > ors.pid

# Check if running
ps aux | grep ors.jar

# Stop later
kill $(cat ors.pid)
```

#### 2. **Screen/Tmux Method** (Most Flexible)
```bash
# Start screen session
screen -S ors-build

# Run inside screen
java -Xms8g -Xmx12g -jar ors-api/target/ors.jar

# Detach: Ctrl+A, then D
# Your process continues running

# Reattach anytime
screen -r ors-build

# List sessions
screen -ls
```

#### 3. **Systemd Service** (Production Ready)
```bash
# Create service file
sudo tee /etc/systemd/system/ors.service > /dev/null <<EOF
[Unit]
Description=OpenRouteService
After=network.target

[Service]
Type=simple
User=your-username
WorkingDirectory=/path/to/openrouteservice
ExecStart=/usr/bin/java -Xms8g -Xmx12g -jar ors-api/target/ors.jar
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# Enable and start
sudo systemctl enable ors
sudo systemctl start ors

# Check status
sudo systemctl status ors
```

## Advanced Monitoring Commands

### Real-time Progress Monitoring
```bash
# Watch memory usage
watch -n 5 'ps aux | grep ors.jar | grep -v grep'

# Monitor log file with timestamps
tail -f ors-docker/logs/ors.log | while read line; do echo "$(date): $line"; done

# Watch graph directory size
watch -n 30 'du -sh ors-docker/graphs/'

# Monitor network connections
watch -n 10 'netstat -tulpn | grep :8082'
```

### Performance Monitoring
```bash
# CPU and memory usage
htop -p $(pgrep -f ors.jar)

# Disk I/O monitoring
iotop -p $(pgrep -f ors.jar)

# Java-specific monitoring
jstat -gc $(pgrep -f ors.jar) 5s
```

## Quick Setup Script

Create `setup-ors.sh`:
```bash
#!/bin/bash
set -e

echo "Setting up OpenRouteService with India map..."

# Check Java
if ! command -v java &> /dev/null; then
    echo "Java not found. Please install Java 21+"
    exit 1
fi

# Build project
echo "Building ORS..."
./mvnw clean package -DskipTests

# Create directories
mkdir -p ors-docker/{graphs,logs}

# Download India map if not exists
if [ ! -f "ors-docker/files/india-latest.osm.pbf" ]; then
    echo "Downloading India map..."
    mkdir -p ors-docker/files
    wget -O ors-docker/files/india-latest.osm.pbf \
        https://download.geofabrik.de/asia/india-latest.osm.pbf
fi

# Backup original config
cp ors-config.yml ors-config.yml.backup

# Update config for India map
sed -i.bak 's|ors-api/src/test/files/heidelberg.test.pbf|./ors-docker/files/india-latest.osm.pbf|g' ors-config.yml

echo "Setup complete! Run with:"
echo "nohup java -Xms8g -Xmx12g -jar ors-api/target/ors.jar > ors.log 2>&1 &"
```

## Next Steps After Setup

1. **Test basic routing functionality**
2. **Explore API documentation** at `http://localhost:8082/swagger-ui`
3. **Set up monitoring** and alerting
4. **Configure reverse proxy** (nginx) for production
5. **Plan backup strategy** for graphs
6. **Consider clustering** for high availability
7. **Integrate with your applications**
8. **Set up log rotation** and monitoring
